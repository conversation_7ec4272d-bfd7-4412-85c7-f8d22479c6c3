# 🚀 Guide de Démarrage - Woézon

## 📋 Prérequis

Avant de commencer, assurez-vous d'avoir installé :

- **Node.js** (v18+) : [Télécharger](https://nodejs.org/)
- **npm** (v8+) : Inclus avec Node.js
- **Git** : [Télécharger](https://git-scm.com/)
- **VS Code** : [Télécharger](https://code.visualstudio.com/) (recommandé)

## 🛠️ Installation

### 1. **<PERSON><PERSON><PERSON> le projet**
```bash
git clone [URL_DU_REPO]
cd AppWoezon
```

### 2. **Installer les dépendances**
```bash
npm install
```

### 3. **Configuration Supabase**
Créez un fichier `.env` à la racine du projet :
```env
EXPO_PUBLIC_SUPABASE_URL=https://xvawtmzqbropjvvjboyy.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
APP_NAME=Woézon
APP_VERSION=1.0.0
```

### 4. **Démarrer le serveur de développement**
```bash
npm start
```

## 📱 Test sur Appareils

### **Web (Ordinateur)**
- Ouvrez http://localhost:8082 dans votre navigateur
- Ou tapez `w` dans le terminal

### **Mobile (iPhone/Android)**
1. Téléchargez **"Expo Go"** depuis l'App Store/Google Play
2. Scannez le QR code affiché dans le terminal
3. L'app se charge automatiquement !

### **Commandes Utiles**
- `w` : Ouvrir dans le navigateur web
- `a` : Ouvrir sur Android
- `i` : Ouvrir sur iOS (macOS uniquement)
- `r` : Recharger l'application
- `m` : Afficher le menu

## 🏗️ Structure du Projet

```
AppWoezon/
├── src/
│   ├── components/          # Composants réutilisables
│   │   ├── common/         # PostCard, StoriesBar
│   │   └── ui/             # LoadingSpinner, Button
│   ├── screens/            # Écrans de l'application
│   │   ├── main/           # FeedScreen, PostDetail
│   │   ├── auth/           # Login, Register
│   │   └── profile/        # UserProfile, Settings
│   ├── navigation/         # Configuration navigation
│   │   ├── MainTabNavigator.tsx
│   │   └── stacks/         # Stack navigators
│   ├── constants/          # Couleurs, Layout
│   ├── services/           # Supabase, API
│   ├── types/              # Types TypeScript
│   └── utils/              # Fonctions utilitaires
├── docs/                   # Documentation
├── .env                    # Variables d'environnement
├── package.json            # Dépendances
└── README.md               # Documentation principale
```

## 🎨 Design System

### **Couleurs**
- **Primary** : #E1306C (Rose Instagram)
- **Background** : #FFFFFF (Blanc)
- **Text** : #262626 (Gris foncé)
- **Border** : #DBDBDB (Gris clair)

### **Typographie**
- **Titre** : 20px, Bold
- **Sous-titre** : 16px, Medium
- **Corps** : 14px, Regular
- **Caption** : 12px, Regular

### **Espacements**
- **xs** : 4px
- **sm** : 8px
- **md** : 16px
- **lg** : 24px
- **xl** : 32px

## 🔧 Développement

### **Scripts Disponibles**
```bash
npm start          # Démarrer Expo
npm run web        # Version web uniquement
npm run android    # Version Android
npm run ios        # Version iOS (macOS)
npm test           # Lancer les tests
npm run lint       # Vérifier le code
npm run format     # Formater le code
```

### **Extensions VS Code Recommandées**
- Expo Tools
- React Native Tools
- TypeScript Importer
- Prettier
- ESLint

## 📊 État Actuel du Projet

### ✅ **Fonctionnalités Implémentées**
- [x] Structure de base du projet
- [x] Navigation par onglets Instagram-like
- [x] Feed avec posts et stories
- [x] Design system complet
- [x] Composants réutilisables
- [x] Configuration Supabase
- [x] Documentation complète

### 🔄 **En Cours de Développement**
- [ ] Authentification utilisateur
- [ ] Création de posts
- [ ] Système de likes et commentaires
- [ ] Chat en temps réel
- [ ] Profils utilisateurs

### 📋 **Prochaines Étapes**
1. **Authentification** : Login/Register avec Supabase
2. **CRUD Posts** : Créer, modifier, supprimer des posts
3. **Interactions** : Likes, commentaires, partages
4. **Chat** : Messagerie en temps réel
5. **IA** : Ami virtuel et suggestions

## 🐛 Dépannage

### **Problèmes Courants**

#### **Port déjà utilisé**
```bash
# Arrêter tous les processus Expo
npx expo stop
# Redémarrer
npm start
```

#### **Erreur de dépendances**
```bash
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
```

#### **Problème de cache**
```bash
# Nettoyer le cache Expo
npx expo start --clear
```

#### **QR Code invisible**
- Tapez `r` dans le terminal pour recharger
- Vérifiez que votre téléphone et ordinateur sont sur le même réseau WiFi

### **Logs et Débogage**
- **Console web** : F12 dans le navigateur
- **Expo DevTools** : Ouvre automatiquement
- **Logs mobile** : Visibles dans Expo Go

## 📞 Support

### **Ressources Utiles**
- [Documentation Expo](https://docs.expo.dev/)
- [React Native Docs](https://reactnative.dev/)
- [Supabase Docs](https://supabase.com/docs)
- [React Navigation](https://reactnavigation.org/)

### **Communauté**
- [Expo Discord](https://discord.gg/expo)
- [React Native Community](https://reactnative.dev/community/overview)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native)

## 🎯 Objectifs de Développement

### **Phase 1 : Base (Actuelle)**
- ✅ Structure et navigation
- ✅ Design system
- ✅ Feed basique

### **Phase 2 : Fonctionnalités Core**
- 🔄 Authentification
- 🔄 CRUD Posts
- 🔄 Interactions sociales

### **Phase 3 : Fonctionnalités Avancées**
- ⏳ Chat temps réel
- ⏳ Live streaming
- ⏳ IA et suggestions

### **Phase 4 : Production**
- ⏳ Tests complets
- ⏳ Optimisations
- ⏳ Déploiement

---

**Woézon** - Votre réseau social nouvelle génération ! 🌟

Pour toute question, consultez la documentation dans le dossier `docs/` ou créez une issue sur GitHub.
