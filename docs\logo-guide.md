# Guide du Logo Woézon

## Vue d'ensemble

Le logo Woézon existe en plusieurs variantes pour s'adapter à différents contextes d'utilisation dans l'application.

## Variantes disponibles

### 1. Logo Moderne (`variant="modern"`)
- **Usage** : Interface principale, headers, navigation
- **Style** : <PERSON><PERSON><PERSON>, adapté aux couleurs de l'app Instagram-style
- **Couleurs** : Utilise la palette de couleurs définie dans `Colors.ts`
- **Caractéristiques** :
  - Forme Z stylisée avec gradients
  - Éléments géométriques minimalistes
  - Optimisé pour les écrans mobiles

### 2. Logo Original (`variant="original"`)
- **Usage** : Splash screen, branding spécial, marketing
- **Style** : Inspiré de votre design original avec formes complexes
- **Couleurs** : Palette colorée vibrante (orange, bleu, vert, jaune)
- **Caractéristiques** :
  - Formes géométriques complexes
  - Motifs décoratifs riches
  - Style artistique et créatif

### 3. Logo Texte (`variant="text"`)
- **Usage** : Contextes où seul le texte est nécessaire
- **Style** : Typographie simple et élégante
- **Couleurs** : Couleur primaire de l'app

## Tailles disponibles

```typescript
size?: 'small' | 'medium' | 'large' | 'xlarge'
```

- **small** (32px) : Icônes, boutons
- **medium** (48px) : Navigation, cards
- **large** (64px) : Headers, profils
- **xlarge** (120px) : Splash screen, onboarding

## Utilisation

### Import
```typescript
import Logo from '../components/common/Logo';
```

### Exemples d'utilisation

```typescript
// Logo principal dans le header
<Logo size="large" variant="modern" showText={false} />

// Logo avec texte pour le branding
<Logo size="medium" variant="modern" showText={true} />

// Logo original pour le splash screen
<Logo size="xlarge" variant="original" showText={true} />

// Logo texte simple
<Logo size="medium" variant="text" />
```

## Props du composant

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `size` | `'small' \| 'medium' \| 'large' \| 'xlarge'` | `'medium'` | Taille du logo |
| `variant` | `'original' \| 'modern' \| 'text'` | `'modern'` | Variante du logo |
| `style` | `ViewStyle` | `undefined` | Styles personnalisés |
| `showText` | `boolean` | `true` | Afficher le texte "Woézon" |

## Recommandations d'usage

### Header principal
```typescript
<Logo size="large" variant="modern" showText={false} />
```

### Navigation tabs
```typescript
<Logo size="small" variant="modern" showText={false} />
```

### Splash screen
```typescript
<Logo size="xlarge" variant="original" showText={true} />
```

### Profil utilisateur
```typescript
<Logo size="medium" variant="modern" showText={true} />
```

## Personnalisation

Le composant utilise les couleurs définies dans `src/constants/Colors.ts`. Pour modifier les couleurs :

1. Modifiez les valeurs dans `Colors.ts`
2. Les gradients s'adapteront automatiquement
3. Le mode sombre est supporté via `darkColors`

## Fichiers concernés

- **Composant** : `src/components/common/Logo.tsx`
- **Couleurs** : `src/constants/Colors.ts`
- **Usage** : `src/screens/HomeScreen.tsx`
- **Documentation** : `docs/logo-guide.md`

## Notes techniques

- Utilise `react-native-svg` pour le rendu vectoriel
- Responsive et adaptatif
- Support du mode sombre
- Optimisé pour les performances
- Compatible iOS/Android/Web
