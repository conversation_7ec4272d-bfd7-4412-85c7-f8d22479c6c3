import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize } from '../../constants/Layout';

const Stack = createStackNavigator();

const ActivityScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>Activité</Text>
    <Text style={styles.description}>Notifications et activité en cours de développement...</Text>
  </View>
);

const ActivityStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="Notifications" 
        component={ActivityScreen}
        options={{ headerTitle: 'Activité' }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  title: {
    fontSize: FontSize.xl,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  description: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});

export default ActivityStackNavigator;
