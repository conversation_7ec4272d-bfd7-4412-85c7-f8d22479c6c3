import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Colors } from '../../constants/Colors';

interface LogoProps {
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  variant?: 'original' | 'modern' | 'text';
  style?: ViewStyle;
  showText?: boolean;
}

const SIZES = {
  small: 32,
  medium: 48,
  large: 64,
  xlarge: 120,
};

export default function Logo({ 
  size = 'medium', 
  variant = 'modern', 
  style,
  showText = true 
}: LogoProps) {
  const logoSize = SIZES[size];
  
  const renderModernLogo = () => (
    <View style={[styles.modernLogo, { width: logoSize, height: logoSize }]}>
      <View style={[styles.modernLogoBackground, { width: logoSize, height: logoSize }]} />
      <View style={styles.modernLogoContent}>
        <Text style={[styles.modernLogoZ, { fontSize: logoSize * 0.6 }]}>Z</Text>
        <View style={styles.modernLogoAccents}>
          <View style={[styles.accent, styles.accent1]} />
          <View style={[styles.accent, styles.accent2]} />
          <View style={[styles.accent, styles.accent3]} />
          <View style={[styles.accent, styles.accent4]} />
        </View>
      </View>
    </View>
  );

  const renderOriginalStyleLogo = () => (
    <View style={[styles.originalLogo, { width: logoSize, height: logoSize }]}>
      <View style={[styles.originalLogoBackground, { width: logoSize, height: logoSize }]} />
      <View style={styles.originalLogoContent}>
        <Text style={[styles.originalLogoZ, { fontSize: logoSize * 0.5 }]}>Z</Text>
        <View style={styles.originalLogoDecorations}>
          <View style={[styles.decoration, styles.decoration1]} />
          <View style={[styles.decoration, styles.decoration2]} />
          <View style={[styles.decoration, styles.decoration3]} />
          <View style={[styles.decoration, styles.decoration4]} />
          <View style={[styles.decoration, styles.decoration5]} />
          <View style={[styles.decoration, styles.decoration6]} />
        </View>
      </View>
    </View>
  );

  const renderTextLogo = () => (
    <Text style={[styles.textLogo, { fontSize: logoSize * 0.4 }]}>
      Woézon
    </Text>
  );

  const renderLogo = () => {
    switch (variant) {
      case 'original':
        return renderOriginalStyleLogo();
      case 'modern':
        return renderModernLogo();
      case 'text':
        return renderTextLogo();
      default:
        return renderModernLogo();
    }
  };

  return (
    <View style={[styles.container, style]}>
      {renderLogo()}
      {showText && variant !== 'text' && (
        <Text style={[styles.logoText, { fontSize: logoSize * 0.2 }]}>
          Woézon
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    fontWeight: 'bold',
    color: Colors.primary,
    marginTop: 4,
    letterSpacing: 0.5,
  },
  textLogo: {
    fontWeight: 'bold',
    color: Colors.primary,
    letterSpacing: 1,
  },
  // Modern Logo Styles
  modernLogo: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modernLogoBackground: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: Colors.primary,
    opacity: 0.1,
  },
  modernLogoContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  modernLogoZ: {
    fontWeight: 'bold',
    color: Colors.primary,
    textShadowColor: Colors.secondary,
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  modernLogoAccents: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  accent: {
    position: 'absolute',
    borderRadius: 10,
  },
  accent1: {
    width: 6,
    height: 6,
    backgroundColor: Colors.accent,
    top: '10%',
    left: '15%',
  },
  accent2: {
    width: 4,
    height: 4,
    backgroundColor: Colors.secondary,
    top: '15%',
    right: '20%',
  },
  accent3: {
    width: 5,
    height: 5,
    backgroundColor: Colors.primary,
    bottom: '15%',
    right: '15%',
  },
  accent4: {
    width: 4,
    height: 4,
    backgroundColor: Colors.accent,
    bottom: '10%',
    left: '20%',
  },
  // Original Logo Styles
  originalLogo: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  originalLogoBackground: {
    position: 'absolute',
    borderRadius: 1000,
    backgroundColor: '#1a1a2e',
  },
  originalLogoContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  originalLogoZ: {
    fontWeight: 'bold',
    color: '#FFD23F',
    textShadowColor: '#FF6B35',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 3,
  },
  originalLogoDecorations: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  decoration: {
    position: 'absolute',
    borderRadius: 10,
  },
  decoration1: {
    width: 8,
    height: 8,
    backgroundColor: '#FF6B35',
    top: '5%',
    left: '10%',
    transform: [{ rotate: '45deg' }],
  },
  decoration2: {
    width: 6,
    height: 6,
    backgroundColor: '#4ECDC4',
    top: '10%',
    right: '15%',
    borderRadius: 15,
  },
  decoration3: {
    width: 7,
    height: 7,
    backgroundColor: '#F7931E',
    bottom: '10%',
    right: '10%',
    transform: [{ rotate: '45deg' }],
  },
  decoration4: {
    width: 5,
    height: 5,
    backgroundColor: '#06FFA5',
    bottom: '5%',
    left: '15%',
    borderRadius: 15,
  },
  decoration5: {
    width: 4,
    height: 4,
    backgroundColor: '#667eea',
    top: '50%',
    left: '5%',
    borderRadius: 10,
  },
  decoration6: {
    width: 4,
    height: 4,
    backgroundColor: '#FFD23F',
    top: '50%',
    right: '5%',
    borderRadius: 10,
  },
});
