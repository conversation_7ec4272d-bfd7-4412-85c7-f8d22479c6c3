import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize } from '../../constants/Layout';

const Stack = createStackNavigator();

const SearchScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>Recherche</Text>
    <Text style={styles.description}>Écran de recherche en cours de développement...</Text>
  </View>
);

const SearchStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="Explore" 
        component={SearchScreen}
        options={{ headerTitle: 'Recherche' }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  title: {
    fontSize: FontSize.xl,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  description: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});

export default SearchStackNavigator;
