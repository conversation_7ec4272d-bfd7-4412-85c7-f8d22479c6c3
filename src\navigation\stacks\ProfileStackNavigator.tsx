import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/Colors';
import { Spacing, FontSize } from '../../constants/Layout';

const Stack = createStackNavigator();

const ProfileScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>Mon Profil</Text>
    <Text style={styles.description}>Profil utilisateur en cours de développement...</Text>
  </View>
);

const ProfileStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="MyProfile" 
        component={ProfileScreen}
        options={{ headerTitle: 'Profil' }}
      />
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  title: {
    fontSize: FontSize.xl,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  description: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});

export default ProfileStackNavigator;
