# Script de nettoyage et installation pour Woézon
# Utilisation: .\scripts\clean-install.ps1

Write-Host "🧹 Nettoyage du projet Woézon..." -ForegroundColor Yellow

# Supprimer les dossiers et fichiers de cache
Write-Host "Suppression de node_modules..." -ForegroundColor Gray
Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue

Write-Host "Suppression des fichiers de lock..." -ForegroundColor Gray
Remove-Item package-lock.json -ErrorAction SilentlyContinue
Remove-Item yarn.lock -ErrorAction SilentlyContinue

# Nettoyer les caches
Write-Host "Nettoyage du cache Yarn..." -ForegroundColor Gray
yarn cache clean

Write-Host "Nettoyage du cache npm..." -ForegroundColor Gray
npm cache clean --force

Write-Host "Nettoyage du cache Expo..." -ForegroundColor Gray
npx expo install --fix

Write-Host "🚀 Installation des dépendances..." -ForegroundColor Green

# Installation avec Yarn
yarn install

Write-Host "✅ Installation terminée!" -ForegroundColor Green
Write-Host "Vous pouvez maintenant lancer: yarn start" -ForegroundColor Cyan
