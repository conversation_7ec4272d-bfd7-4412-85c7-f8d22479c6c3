# Installation et Configuration du Logo Woézon

## État actuel

✅ **Composant Logo créé** : `src/components/common/Logo.tsx`
✅ **Intégration HomeScreen** : Logo affiché sur l'écran d'accueil
✅ **Documentation** : Guide d'utilisation disponible
⏳ **react-native-svg** : À installer pour les logos vectoriels optimaux

## Installation de react-native-svg (Recommandé)

Pour obtenir des logos vectoriels de haute qualité, installez react-native-svg :

### 1. Installation du package
```bash
npm install react-native-svg
```

### 2. Installation pour iOS (si applicable)
```bash
cd ios && pod install
```

### 3. Configuration Metro (React Native 0.60+)
Ajoutez dans `metro.config.js` :
```javascript
const { getDefaultConfig } = require('metro-config');

module.exports = (async () => {
  const {
    resolver: { sourceExts, assetExts },
  } = await getDefaultConfig();
  return {
    transformer: {
      babelTransformerPath: require.resolve('react-native-svg-transformer'),
    },
    resolver: {
      assetExts: assetExts.filter(ext => ext !== 'svg'),
      sourceExts: [...sourceExts, 'svg'],
    },
  };
})();
```

### 4. Installation du transformer SVG
```bash
npm install --save-dev react-native-svg-transformer
```

## Version actuelle (sans SVG)

Le composant Logo fonctionne actuellement avec des styles CSS natifs :
- ✅ Logo moderne avec forme Z stylisée
- ✅ Logo original avec décorations colorées  
- ✅ Logo texte simple
- ✅ Toutes les tailles supportées
- ✅ Mode sombre compatible

## Mise à niveau vers SVG (Optionnel)

Une fois react-native-svg installé, vous pourrez :

1. **Remplacer les styles CSS** par des composants SVG vectoriels
2. **Importer votre logo original** comme fichier SVG
3. **Créer des animations** de logo
4. **Optimiser la qualité** sur tous les écrans

## Utilisation actuelle

```typescript
import Logo from '../components/common/Logo';

// Logo moderne (recommandé)
<Logo size="large" variant="modern" showText={false} />

// Logo original coloré
<Logo size="medium" variant="original" showText={true} />

// Logo texte simple
<Logo size="small" variant="text" />
```

## Fichiers de logo

Pour ajouter votre logo original comme image :

1. **Sauvegardez votre logo** dans `src/assets/images/`
   - `logo-original.png` (version haute résolution)
   - `logo-icon.png` (version icône 512x512)
   - `logo-splash.png` (version splash screen)

2. **Formats recommandés** :
   - PNG avec transparence
   - Résolutions multiples (@1x, @2x, @3x)
   - SVG pour la scalabilité parfaite

## Prochaines étapes

1. ✅ **Tester le logo actuel** dans l'application
2. ⏳ **Installer react-native-svg** quand les autres dépendances sont prêtes
3. ⏳ **Optimiser les logos** avec des versions SVG
4. ⏳ **Ajouter des animations** de logo (optionnel)
5. ⏳ **Créer un splash screen** avec le logo

## Support

Le logo fonctionne parfaitement dans l'état actuel. L'installation de react-native-svg est optionnelle mais recommandée pour une qualité optimale.
