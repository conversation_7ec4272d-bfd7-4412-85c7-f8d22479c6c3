import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Colors } from '../constants/Colors';
import { Spacing, FontSize, FontWeight } from '../constants/Layout';
import Logo from '../components/common/Logo';

export default function HomeScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Logo size="large" variant="modern" showText={false} />
          <Text style={styles.logoText}>Woézon</Text>
          <Text style={styles.subtitle}>Votre réseau social nouvelle génération</Text>

          {/* Logo variants showcase */}
          <View style={styles.logoShowcase}>
            <Text style={styles.showcaseTitle}>Versions du logo :</Text>
            <View style={styles.logoRow}>
              <View style={styles.logoVariant}>
                <Logo size="medium" variant="modern" showText={false} />
                <Text style={styles.variantLabel}>Moderne</Text>
              </View>
              <View style={styles.logoVariant}>
                <Logo size="medium" variant="original" showText={false} />
                <Text style={styles.variantLabel}>Original</Text>
              </View>
              <View style={styles.logoVariant}>
                <Logo size="medium" variant="text" showText={false} />
                <Text style={styles.variantLabel}>Texte</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Bienvenue sur Woézon ! 🎉</Text>
          <Text style={styles.welcomeText}>
            Découvrez une nouvelle façon de vous connecter avec le monde.
            Partagez vos moments, découvrez du contenu inspirant et 
            connectez-vous avec des personnes du monde entier...
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Fonctionnalités à venir</Text>
          
          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>📸 Partage de photos et vidéos</Text>
            <Text style={styles.featureDescription}>
              Partagez vos moments préférés avec une interface inspirée d'Instagram
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>💬 Chat en temps réel</Text>
            <Text style={styles.featureDescription}>
              Discutez avec vos amis en privé ou en groupe
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>🌍 Découverte mondiale</Text>
            <Text style={styles.featureDescription}>
              Connectez-vous avec des personnes du monde entier
            </Text>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureTitle}>💼 Opportunités professionnelles</Text>
            <Text style={styles.featureDescription}>
              Trouvez des opportunités d'emploi et développez votre réseau
            </Text>
          </View>
        </View>

        {/* Status Section */}
        <View style={styles.statusSection}>
          <Text style={styles.statusTitle}>État du développement</Text>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>✅ Configuration de base</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>✅ Supabase configuré</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>🔄 Interface utilisateur</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>⏳ Authentification</Text>
          </View>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>⏳ Fonctionnalités sociales</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg,
  },
  logoText: {
    fontSize: FontSize.xxl,
    fontWeight: FontWeight.bold,
    color: Colors.primary,
    marginTop: Spacing.sm,
    marginBottom: Spacing.sm,
    letterSpacing: 1,
  },
  logoShowcase: {
    marginTop: Spacing.xl,
    padding: Spacing.lg,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 12,
    width: '100%',
  },
  showcaseTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  logoRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  logoVariant: {
    alignItems: 'center',
    flex: 1,
  },
  variantLabel: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  welcomeSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  welcomeTitle: {
    fontSize: FontSize.xl,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.md,
  },
  welcomeText: {
    fontSize: FontSize.md,
    color: Colors.textSecondary,
    lineHeight: 24,
  },
  featuresSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.lg,
  },
  featureCard: {
    backgroundColor: Colors.backgroundSecondary,
    padding: Spacing.lg,
    borderRadius: 12,
    marginBottom: Spacing.md,
  },
  featureTitle: {
    fontSize: FontSize.md,
    fontWeight: FontWeight.semibold,
    color: Colors.text,
    marginBottom: Spacing.sm,
  },
  featureDescription: {
    fontSize: FontSize.sm,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
  statusSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  statusTitle: {
    fontSize: FontSize.lg,
    fontWeight: FontWeight.bold,
    color: Colors.text,
    marginBottom: Spacing.lg,
  },
  statusItem: {
    marginBottom: Spacing.sm,
  },
  statusLabel: {
    fontSize: FontSize.md,
    color: Colors.text,
  },
});
